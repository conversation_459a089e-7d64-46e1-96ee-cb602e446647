import React, { forwardRef, useImperativeHandle, useState, useCallback, useEffect } from 'react';
import { Box, Text, HStack, Avatar, Badge } from '@chakra-ui/react';
import { useFormContext } from 'react-hook-form';
import Select from 'react-select';
import { components } from 'react-select';

import { useOptimisticVendedores } from 'hooks/pdv/useOptimisticPdv';
import { SkeletonSelect, SkeletonOptimistic } from 'components/PDV/SkeletonOptimistic';

interface SelectVendedorOptimisticProps {
  id: string;
  name: string;
  label?: string;
  placeholder?: string;
  required?: boolean;
  size?: 'sm' | 'md' | 'lg';
  isDisabled?: boolean;
  onClick?: () => void;
}

interface VendedorOption {
  label: string;
  value: string;
}

export interface SelectVendedorOptimisticRef {
  handleGetVendedorSelecionado: () => VendedorOption | undefined;
  vendedores: VendedorOption[];
}

// Componente customizado para opções de vendedor
const OptionComponent = (props: any) => {
  const { data } = props;

  return (
    <components.Option {...props}>
      <HStack spacing={3}>
        <Avatar 
          size="sm" 
          name={data.label}
          bg="green.500"
        />
        <Box flex={1}>
          <Text fontSize="sm" fontWeight="medium">
            {data.label}
          </Text>
          <Badge size="sm" colorScheme="green" variant="subtle">
            Vendedor
          </Badge>
        </Box>
      </HStack>
    </components.Option>
  );
};

// Componente para valor selecionado
const SingleValueComponent = (props: any) => {
  const { data } = props;

  return (
    <components.SingleValue {...props}>
      <HStack spacing={2}>
        <Avatar 
          size="xs" 
          name={data.label}
          bg="green.500"
        />
        <Text fontSize="sm">
          {data.label}
        </Text>
      </HStack>
    </components.SingleValue>
  );
};

// Componente para placeholder customizado
const PlaceholderComponent = (props: any) => (
  <components.Placeholder {...props}>
    <HStack spacing={2}>
      <Avatar size="xs" bg="gray.300" />
      <Text fontSize="sm" color="gray.500">
        {props.children}
      </Text>
    </HStack>
  </components.Placeholder>
);

const SelectVendedorOptimistic = forwardRef<
  SelectVendedorOptimisticRef,
  SelectVendedorOptimisticProps
>(({ id, name, label, placeholder, required, size = 'md', isDisabled, onClick }, ref) => {
  const { setValue, watch } = useFormContext();
  const [selectedVendedor, setSelectedVendedor] = useState<VendedorOption | null>(null);
  
  const {
    vendedores,
    isLoading,
    error,
  } = useOptimisticVendedores();

  const currentValue = watch(name);

  // Função para lidar com mudança de seleção
  const handleChange = useCallback((option: VendedorOption | null) => {
    setSelectedVendedor(option);
    setValue(name, option?.value || '');
  }, [setValue, name]);

  // Função para obter vendedor selecionado
  const handleGetVendedorSelecionado = useCallback(() => {
    return selectedVendedor || undefined;
  }, [selectedVendedor]);

  // Expor métodos via ref
  useImperativeHandle(ref, () => ({
    handleGetVendedorSelecionado,
    vendedores,
  }));

  // Sincronizar com valor do formulário
  useEffect(() => {
    if (currentValue && typeof currentValue === 'string') {
      const vendedor = vendedores.find(v => v.value === currentValue);
      if (vendedor && vendedor !== selectedVendedor) {
        setSelectedVendedor(vendedor);
      }
    } else if (currentValue && currentValue !== selectedVendedor) {
      setSelectedVendedor(currentValue);
    }
  }, [currentValue, vendedores, selectedVendedor]);

  // Estilos customizados para o select
  const customStyles = {
    control: (provided: any, state: any) => ({
      ...provided,
      minHeight: size === 'lg' ? '48px' : size === 'sm' ? '32px' : '40px',
      borderColor: state.isFocused ? '#38a169' : '#e2e8f0',
      boxShadow: state.isFocused ? '0 0 0 1px #38a169' : 'none',
      cursor: onClick ? 'pointer' : 'default',
      '&:hover': {
        borderColor: '#cbd5e0',
      },
    }),
    option: (provided: any, state: any) => ({
      ...provided,
      backgroundColor: state.isSelected 
        ? '#38a169' 
        : state.isFocused 
        ? '#f0fff4' 
        : 'white',
      color: state.isSelected ? 'white' : '#2d3748',
      padding: '8px 12px',
    }),
    menu: (provided: any) => ({
      ...provided,
      zIndex: 9999,
      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    }),
    singleValue: (provided: any) => ({
      ...provided,
      display: 'flex',
      alignItems: 'center',
    }),
    placeholder: (provided: any) => ({
      ...provided,
      display: 'flex',
      alignItems: 'center',
    }),
  };

  // Se está carregando e não tem dados, mostrar skeleton
  if (isLoading && vendedores.length === 0) {
    return <SkeletonSelect label={label} showLabel={!!label} />;
  }

  // Se há erro, mostrar select desabilitado com mensagem
  if (error) {
    return (
      <Box>
        {label && (
          <Text fontSize="sm" fontWeight="medium" mb={2} color="gray.700">
            {label}
            {required && <Text as="span" color="red.500" ml={1}>*</Text>}
          </Text>
        )}
        <Select
          isDisabled
          placeholder="Erro ao carregar vendedores"
          styles={{
            ...customStyles,
            control: (provided: any) => ({
              ...provided,
              borderColor: '#fed7d7',
              backgroundColor: '#fef5e7',
            }),
          }}
        />
      </Box>
    );
  }

  return (
    <Box>
      {label && (
        <Text fontSize="sm" fontWeight="medium" mb={2} color="gray.700">
          {label}
          {required && <Text as="span" color="red.500" ml={1}>*</Text>}
        </Text>
      )}
      
      <SkeletonOptimistic isOptimistic={isLoading}>
        <Select
          id={id}
          name={name}
          value={selectedVendedor}
          onChange={handleChange}
          options={vendedores}
          placeholder={placeholder || 'Selecione um vendedor'}
          isDisabled={isDisabled}
          isClearable
          isSearchable
          styles={customStyles}
          components={{
            Option: OptionComponent,
            SingleValue: SingleValueComponent,
            Placeholder: PlaceholderComponent,
          }}
          onMenuOpen={onClick} // Abrir modal se necessário
          filterOption={(option, inputValue) => {
            return option.label.toLowerCase().includes(inputValue.toLowerCase());
          }}
          menuPortalTarget={document.body}
          menuPosition="fixed"
        />
      </SkeletonOptimistic>
    </Box>
  );
});

SelectVendedorOptimistic.displayName = 'SelectVendedorOptimistic';

export default SelectVendedorOptimistic;
