import { Box, Text, VStack, HStack, Avatar, Spinner } from '@chakra-ui/react';
import React, {
  forwardRef,
  useImperativeHandle,
  useState,
  useCallback,
  useEffect,
} from 'react';
import { useFormContext } from 'react-hook-form';
import { components } from 'react-select';
import AsyncSelect from 'react-select/async';

import { useOptimisticClientes } from 'hooks/pdv/useOptimisticPdv';

import {
  SkeletonSelect,
  SkeletonOptimistic,
} from 'components/PDV/SkeletonOptimistic';

interface SelectClienteOptimisticProps {
  id: string;
  name: string;
  label?: string;
  placeholder?: string;
  required?: boolean;
  size?: 'sm' | 'md' | 'lg';
  isDisabled?: boolean;
}

interface ClienteOption {
  label: string;
  value: string;
  obj?: any;
}

export interface SelectClienteOptimisticRef {
  setCliente: (cliente: any) => void;
  getCliente: () => ClienteOption | null;
}

// Componente customizado para opções com avatar e informações
const OptionComponent = (props: any) => {
  const { data } = props;
  const cliente = data.obj;

  return (
    <components.Option {...props}>
      <HStack spacing={3}>
        <Avatar size="sm" name={cliente?.nome || data.label} bg="blue.500" />
        <VStack align="start" spacing={0}>
          <Text fontSize="sm" fontWeight="medium">
            {cliente?.nome || data.label}
          </Text>
          {cliente?.documento && (
            <Text fontSize="xs" color="gray.500">
              {cliente.documento}
            </Text>
          )}
          {cliente?.endereco && (
            <Text fontSize="xs" color="gray.400" noOfLines={1}>
              {cliente.endereco}
            </Text>
          )}
        </VStack>
      </HStack>
    </components.Option>
  );
};

// Componente para mostrar loading nas opções
const LoadingMessage = () => (
  <HStack spacing={2} p={2} justify="center">
    <Spinner size="sm" color="blue.500" />
    <Text fontSize="sm" color="gray.500">
      Buscando clientes...
    </Text>
  </HStack>
);

// Componente para mostrar quando não há resultados
const NoOptionsMessage = (props: any) => (
  <components.NoOptionsMessage {...props}>
    <Text fontSize="sm" color="gray.500" textAlign="center" p={2}>
      {props.inputValue
        ? 'Nenhum cliente encontrado'
        : 'Digite para buscar clientes'}
    </Text>
  </components.NoOptionsMessage>
);

const SelectClienteOptimistic = forwardRef<
  SelectClienteOptimisticRef,
  SelectClienteOptimisticProps
>(
  (
    { id, name, label, placeholder, required, size = 'md', isDisabled },
    ref
  ) => {
    const { setValue, watch } = useFormContext();
    const [selectedCliente, setSelectedCliente] =
      useState<ClienteOption | null>(null);
    const [inputValue, setInputValue] = useState('');

    const {
      clientesOptions,
      isLoading,
      buscarClientes,
      prefetchCliente,
      clientesPopulares,
    } = useOptimisticClientes();

    const currentValue = watch(name);

    // Função para carregar opções com debounce
    const loadOptions = useCallback(
      (inputValue: string, callback: (options: ClienteOption[]) => void) => {
        if (inputValue.length < 2) {
          // Mostrar clientes populares quando não há busca
          callback(clientesPopulares);
          return;
        }

        // Buscar clientes
        buscarClientes(inputValue);

        // Usar timeout para simular debounce
        setTimeout(() => {
          callback(clientesOptions);
        }, 300);
      },
      [buscarClientes, clientesOptions, clientesPopulares]
    );

    // Função para lidar com mudança de seleção
    const handleChange = useCallback(
      (option: ClienteOption | null) => {
        setSelectedCliente(option);
        setValue(name, option);

        // Prefetch dados do cliente selecionado
        if (option?.value) {
          prefetchCliente(option.value);
        }
      },
      [setValue, name, prefetchCliente]
    );

    // Função para lidar com mudança de input
    const handleInputChange = useCallback((newValue: string) => {
      setInputValue(newValue);
      return newValue;
    }, []);

    // Expor métodos via ref
    useImperativeHandle(ref, () => ({
      setCliente: (cliente: any) => {
        const option: ClienteOption = {
          label: cliente.nome || cliente.descricao,
          value: cliente.id,
          obj: cliente,
        };
        setSelectedCliente(option);
        setValue(name, option);
      },
      getCliente: () => selectedCliente,
    }));

    // Sincronizar com valor do formulário
    useEffect(() => {
      if (currentValue && currentValue !== selectedCliente) {
        setSelectedCliente(currentValue);
      }
    }, [currentValue, selectedCliente]);

    // Estilos customizados para o select
    const customStyles = {
      control: (provided: any, state: any) => ({
        ...provided,
        minHeight: size === 'lg' ? '48px' : size === 'sm' ? '32px' : '40px',
        borderColor: state.isFocused ? '#3182ce' : '#e2e8f0',
        boxShadow: state.isFocused ? '0 0 0 1px #3182ce' : 'none',
        '&:hover': {
          borderColor: '#cbd5e0',
        },
      }),
      option: (provided: any, state: any) => ({
        ...provided,
        backgroundColor: state.isSelected
          ? '#3182ce'
          : state.isFocused
          ? '#ebf8ff'
          : 'white',
        color: state.isSelected ? 'white' : '#2d3748',
        padding: '8px 12px',
      }),
      menu: (provided: any) => ({
        ...provided,
        zIndex: 9999,
        boxShadow:
          '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
      }),
      loadingMessage: (provided: any) => ({
        ...provided,
        padding: '8px 12px',
      }),
    };

    return (
      <Box>
        {label && (
          <Text fontSize="sm" fontWeight="medium" mb={2} color="gray.700">
            {label}
            {required && (
              <Text as="span" color="red.500" ml={1}>
                *
              </Text>
            )}
          </Text>
        )}

        <SkeletonOptimistic isOptimistic={isLoading && !clientesOptions.length}>
          <AsyncSelect
            id={id}
            name={name}
            value={selectedCliente}
            onChange={handleChange}
            onInputChange={handleInputChange}
            loadOptions={loadOptions}
            defaultOptions={clientesPopulares}
            placeholder={placeholder || 'Digite para buscar clientes...'}
            noOptionsMessage={NoOptionsMessage}
            loadingMessage={LoadingMessage}
            isDisabled={isDisabled}
            isClearable
            isSearchable
            cacheOptions
            styles={customStyles}
            components={{
              Option: OptionComponent,
              LoadingMessage,
              NoOptionsMessage,
            }}
            filterOption={() => true} // Desabilitar filtro local
            menuPortalTarget={document.body}
            menuPosition="fixed"
          />
        </SkeletonOptimistic>
      </Box>
    );
  }
);

SelectClienteOptimistic.displayName = 'SelectClienteOptimistic';

export default SelectClienteOptimistic;
